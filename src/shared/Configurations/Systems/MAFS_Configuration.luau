--!strict

--[[
    - file: MAFS_Configuration.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - MAFS Configuration Module
      - Centralizes material sound definitions
      - Manages system-wide settings and constants
      - Enables debug mode and performance monitoring
      - Defines security and rate limiting parameters
      - Used by both client and server components
]]

-- ============================================================================
-- DEFINE LUAU MODULE
-- ============================================================================
local MAFSConfig = {} :: MAFSConfigType

-- ============================================================================
-- TYPE ANNOTATIONS & INFER USAGE
-- ============================================================================

-- Playback speed range tuple (min, max)
type PlaybackSpeedRange = { number }

-- Material data structure
type MaterialData = {
  SoundIds: { string },
  Volume: number,
  PlaybackSpeedRange: PlaybackSpeedRange,
  RollOffMinDistance: number,
  RollOffMaxDistance: number,
}

-- MAFS Settings structure
type MAFSSettings = {
  DebugMode: boolean,
  EnablePerformanceMetrics: boolean,
  MaxCachedSounds: number,
  BroadcastRadius: number,
  ServerCooldown: number,
  ClientCooldown: number,
  MovementThreshold: number,
  StepInterval: number,
  MaxDistanceDelta: number,
}

-- MAFS Configuration structure
type MAFSConfigType = {
  Settings: MAFSSettings,
  MaterialSounds: { [Enum.Material]: MaterialData },
  CustomMaterials: { [string]: MaterialData },
  DefaultMaterial: MaterialData,

  GetMaterialData: (material: Enum.Material | string) -> MaterialData,
  GetRandomSoundId: (materialData: MaterialData) -> string,
  GetRandomPlaybackSpeed: (materialData: MaterialData) -> number,
  IsDebugMode: () -> boolean,
  SetDebugMode: (enabled: boolean) -> (),
}

-- ============================================================================
-- MAFS SYSTEM SETTINGS
-- ============================================================================
MAFSConfig.Settings = {
  DebugMode = false,
  EnablePerformanceMetrics = true,
  MaxCachedSounds = 20,
  BroadcastRadius = 50,
  ServerCooldown = 0.25,
  ClientCooldown = 0.27, -- Slightly higher than server to prevent spam
  MovementThreshold = 0.1,
  StepInterval = 0.3,
  MaxDistanceDelta = 10, -- Anti-teleport protection
} :: MAFSSettings

-- ============================================================================
-- MATERIAL SOUND DEFINITIONS
-- ============================================================================
MAFSConfig.MaterialSounds = {
  [Enum.Material.Grass] = {
    SoundIds = {
      "rbxassetid://9118663153",
      "rbxassetid://9118663295",
      "rbxassetid://9118663485",
    },
    Volume = 0.8,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  },

  [Enum.Material.Sand] = {
    SoundIds = {
      "rbxassetid://9118663775",
      "rbxassetid://9118664041",
      "rbxassetid://9118664270",
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 45,
  },

  [Enum.Material.Concrete] = {
    SoundIds = {
      "rbxassetid://9118662067",
      "rbxassetid://9118662323",
      "rbxassetid://9118662536",
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  },

  [Enum.Material.Wood] = {
    SoundIds = {
      "rbxassetid://9118664500",
      "rbxassetid://9118664650",
      "rbxassetid://9118664800",
    },
    Volume = 0.9,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  },

  [Enum.Material.Metal] = {
    SoundIds = {
      "rbxassetid://9118665000",
      "rbxassetid://9118665150",
      "rbxassetid://9118665300",
    },
    Volume = 1.1,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 7,
    RollOffMaxDistance = 60,
  },

  [Enum.Material.Rock] = {
    SoundIds = {
      "rbxassetid://9118665500",
      "rbxassetid://9118665650",
      "rbxassetid://9118665800",
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  },

  [Enum.Material.Water] = {
    SoundIds = {
      "rbxassetid://9118666000",
      "rbxassetid://9118666150",
      "rbxassetid://9118666300",
    },
    Volume = 0.6,
    PlaybackSpeedRange = { 0.8, 1.2 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  },
} :: { [Enum.Material]: MaterialData }

-- ============================================================================
-- CUSTOM MATERIAL SOUND DEFINITIONS (via FootstepMaterial attribute)
-- ============================================================================
MAFSConfig.CustomMaterials = {
  ["Snow"] = {
    SoundIds = {
      "rbxassetid://9118666500",
      "rbxassetid://9118666650",
      "rbxassetid://9118666800",
    },
    Volume = 0.5,
    PlaybackSpeedRange = { 0.8, 1.0 },
    RollOffMinDistance = 3,
    RollOffMaxDistance = 35,
  },

  ["Gravel"] = {
    SoundIds = {
      "rbxassetid://9118667000",
      "rbxassetid://9118667150",
      "rbxassetid://9118667300",
    },
    Volume = 0.9,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  },

  ["Mud"] = {
    SoundIds = {
      "rbxassetid://9118667500",
      "rbxassetid://9118667650",
      "rbxassetid://9118667800",
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.8, 1.0 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  },

  ["MetalGrate"] = {
    SoundIds = {
      "rbxassetid://9118668000",
      "rbxassetid://9118668150",
      "rbxassetid://9118668300",
    },
    Volume = 1.2,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 8,
    RollOffMaxDistance = 65,
  },
} :: { [string]: MaterialData }

-- ============================================================================
-- DEFAULT MATERIAL SOUND DEFINITIONS (fallback)
-- ============================================================================
MAFSConfig.DefaultMaterial = {
  SoundIds = {
    "rbxassetid://9118662067",
    "rbxassetid://9118662323",
  },
  Volume = 0.8,
  PlaybackSpeedRange = { 0.9, 1.1 },
  RollOffMinDistance = 5,
  RollOffMaxDistance = 50,
} :: MaterialData

-- ============================================================================
-- MAFS UTILITY FUNCTIONS
-- ============================================================================

-- Material data retrieval
function MAFSConfig.GetMaterialData(material: Enum.Material | string): MaterialData
  -- First check if it's a standard Roblox material
  if typeof(material) == "EnumItem" and MAFSConfig.MaterialSounds[material :: Enum.Material] then
    return MAFSConfig.MaterialSounds[material :: Enum.Material]
  end

  -- Then check if it's a custom material (string)
  if type(material) == "string" and MAFSConfig.CustomMaterials[material] then
    return MAFSConfig.CustomMaterials[material]
  end

  -- Return default if not found
  return MAFSConfig.DefaultMaterial
end

-- Sound and playback utilities
function MAFSConfig.GetRandomSoundId(materialData: MaterialData): string
  local soundIds: { string } = materialData.SoundIds
  return soundIds[math.random(1, #soundIds)]
end

-- Returns a random playback speed within the material's defined range
function MAFSConfig.GetRandomPlaybackSpeed(materialData: MaterialData): number
  local range: PlaybackSpeedRange = materialData.PlaybackSpeedRange
  return range[1] + math.random() * (range[2] - range[1])
end

-- Debug mode utilities
function MAFSConfig.IsDebugMode(): boolean
  return MAFSConfig.Settings.DebugMode
end

-- Enables or disables debug mode
function MAFSConfig.SetDebugMode(enabled: boolean): ()
  MAFSConfig.Settings.DebugMode = enabled
end

-- ============================================================================
-- EXPORT LUAU MODULE
-- ============================================================================
return MAFSConfig :: MAFSConfigType
